---
server:
  http2:
    enabled: true
  max-http-request-header-size: 1MB
  shutdown: graceful
  port: 8080

spring:
  application:
    name: scm-procurement-data-integration
  datasource:
    driverClassName: org.postgresql.Driver
    url: ${DB_URL:*************************************************}
    username: ${DB_USERNAME:procurement_user}
    password: ${DB_PASSWORD:procurement_pass}
    hikari:
      connection-timeout: 30000 # 30 seconds
      idle-timeout: 300000 # 5 minutes
      maximum-pool-size: 10
      minimum-idle: 2
      connection-test-query: "SELECT 1"
      validation-timeout: 5000 # 5 second
      max-lifetime: 1800000 # 30 minutes
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate.ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  lifecycle:
    timeout-per-shutdown-phase: 30s
  servlet:
    multipart:
      max-file-size: 250KB
      max-request-size: 250KB
  security:
    oauth2:
      resourceserver:
        jwt:
          secret-key: ${AUTH_SERVICE_JWT_SECRET_KEY:default-secret-key}
          issuer-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID:default-tenant}/v2.0
          jwk-set-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID:default-tenant}/discovery/v2.0/keys

management:
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState,diskSpace,db,ping
        liveness:
          include: livenessState,diskSpace,db,ping
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  info:
    git:
      mode: full
      enabled: true
  tracing:
    propagation:
      type: B3,W3C
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://${OTLP_EXPORTER_HOST:localhost}:9411/api/v2/spans

logging:
  pattern:
    level: "%5p [%MDC{traceId},%MDC{spanId}]"
    console: "%d{yyyy-MM-dd HH:mm:ss} %5p [%MDC{traceId},%MDC{spanId}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} [%MDC{traceId},%MDC{spanId}] - %msg%n"
  level:
    com.hellofresh.scm: INFO
    org.springframework: INFO
    org.springframework.security: DEBUG

webclient:
  connection-timeout: 5s
  response-timeout: 5s
  max-idle-time: 20s
  max-life-time: 60s
  pending-acquire-timeout: 60s
  evict-in-background: 120s

statsig:
  api-key: ${STATSIG_API_KEY:default-statsig-key}
  environment: ${STATSIG_ENVIRONMENT:local}

resilience4j:
  retry:
    instances:
      processData:
        maxAttempts: 3
        waitDuration: 2s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2