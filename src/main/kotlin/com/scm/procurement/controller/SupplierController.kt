package com.scm.procurement.controller

import com.scm.procurement.dto.CreateSupplierRequest
import com.scm.procurement.dto.SupplierResponse
import com.scm.procurement.dto.UpdateSupplierRequest
import com.scm.procurement.service.SupplierService
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/suppliers")
@CrossOrigin(origins = ["*"])
class SupplierController(
    private val supplierService: SupplierService
) {

    @PostMapping
    fun createSupplier(@Valid @RequestBody request: CreateSupplierRequest): ResponseEntity<SupplierResponse> {
        val supplier = supplierService.createSupplier(request)
        return ResponseEntity.status(HttpStatus.CREATED).body(supplier)
    }

    @GetMapping("/{id}")
    fun getSupplierById(@PathVariable id: Long): ResponseEntity<SupplierResponse> {
        val supplier = supplierService.getSupplierById(id)
        return ResponseEntity.ok(supplier)
    }

    @GetMapping
    fun getAllSuppliers(
        @RequestParam(required = false) activeOnly: Boolean = false,
        @RequestParam(required = false) search: String?
    ): ResponseEntity<List<SupplierResponse>> {
        val suppliers = when {
            !search.isNullOrBlank() -> supplierService.searchSuppliersByName(search)
            activeOnly -> supplierService.getActiveSuppliers()
            else -> supplierService.getAllSuppliers()
        }
        return ResponseEntity.ok(suppliers)
    }

    @PutMapping("/{id}")
    fun updateSupplier(
        @PathVariable id: Long,
        @Valid @RequestBody request: UpdateSupplierRequest
    ): ResponseEntity<SupplierResponse> {
        val supplier = supplierService.updateSupplier(id, request)
        return ResponseEntity.ok(supplier)
    }

    @DeleteMapping("/{id}")
    fun deleteSupplier(@PathVariable id: Long): ResponseEntity<Void> {
        supplierService.deleteSupplier(id)
        return ResponseEntity.noContent().build()
    }

    @PatchMapping("/{id}/deactivate")
    fun deactivateSupplier(@PathVariable id: Long): ResponseEntity<SupplierResponse> {
        val supplier = supplierService.deactivateSupplier(id)
        return ResponseEntity.ok(supplier)
    }
}
