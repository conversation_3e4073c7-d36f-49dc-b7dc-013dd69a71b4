package com.scm.procurement.service

import com.scm.procurement.dto.CreateSupplierRequest
import com.scm.procurement.dto.SupplierResponse
import com.scm.procurement.dto.UpdateSupplierRequest
import com.scm.procurement.exception.ResourceNotFoundException
import com.scm.procurement.exception.DuplicateResourceException
import com.scm.procurement.model.Supplier
import com.scm.procurement.repository.SupplierRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
@Transactional
class SupplierService(
    private val supplierRepository: SupplierRepository
) {

    fun createSupplier(request: CreateSupplierRequest): SupplierResponse {
        // Check if email already exists
        request.email?.let { email ->
            if (supplierRepository.existsByEmail(email)) {
                throw DuplicateResourceException("Supplier with email $email already exists")
            }
        }

        val supplier = Supplier(
            name = request.name,
            email = request.email,
            phone = request.phone,
            address = request.address
        )

        val savedSupplier = supplierRepository.save(supplier)
        return mapToResponse(savedSupplier)
    }

    @Transactional(readOnly = true)
    fun getSupplierById(id: Long): SupplierResponse {
        val supplier = supplierRepository.findById(id)
            .orElseThrow { ResourceNotFoundException("Supplier not found with id: $id") }
        return mapToResponse(supplier)
    }

    @Transactional(readOnly = true)
    fun getAllSuppliers(): List<SupplierResponse> {
        return supplierRepository.findAll().map { mapToResponse(it) }
    }

    @Transactional(readOnly = true)
    fun getActiveSuppliers(): List<SupplierResponse> {
        return supplierRepository.findByIsActiveTrue().map { mapToResponse(it) }
    }

    @Transactional(readOnly = true)
    fun searchSuppliersByName(name: String): List<SupplierResponse> {
        return supplierRepository.findActiveSuppliersByNameContaining(name)
            .map { mapToResponse(it) }
    }

    fun updateSupplier(id: Long, request: UpdateSupplierRequest): SupplierResponse {
        val existingSupplier = supplierRepository.findById(id)
            .orElseThrow { ResourceNotFoundException("Supplier not found with id: $id") }

        // Check email uniqueness if email is being updated
        request.email?.let { newEmail ->
            if (newEmail != existingSupplier.email && supplierRepository.existsByEmail(newEmail)) {
                throw DuplicateResourceException("Supplier with email $newEmail already exists")
            }
        }

        val updatedSupplier = existingSupplier.copy(
            name = request.name ?: existingSupplier.name,
            email = request.email ?: existingSupplier.email,
            phone = request.phone ?: existingSupplier.phone,
            address = request.address ?: existingSupplier.address,
            isActive = request.isActive ?: existingSupplier.isActive,
            updatedAt = LocalDateTime.now()
        )

        val savedSupplier = supplierRepository.save(updatedSupplier)
        return mapToResponse(savedSupplier)
    }

    fun deleteSupplier(id: Long) {
        if (!supplierRepository.existsById(id)) {
            throw ResourceNotFoundException("Supplier not found with id: $id")
        }
        supplierRepository.deleteById(id)
    }

    fun deactivateSupplier(id: Long): SupplierResponse {
        val supplier = supplierRepository.findById(id)
            .orElseThrow { ResourceNotFoundException("Supplier not found with id: $id") }

        val deactivatedSupplier = supplier.copy(
            isActive = false,
            updatedAt = LocalDateTime.now()
        )

        val savedSupplier = supplierRepository.save(deactivatedSupplier)
        return mapToResponse(savedSupplier)
    }

    private fun mapToResponse(supplier: Supplier): SupplierResponse {
        return SupplierResponse(
            id = supplier.id,
            name = supplier.name,
            email = supplier.email,
            phone = supplier.phone,
            address = supplier.address,
            isActive = supplier.isActive,
            createdAt = supplier.createdAt,
            updatedAt = supplier.updatedAt
        )
    }
}
