package com.scm.procurement.repository

import com.scm.procurement.model.Supplier
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface SupplierRepository : JpaRepository<Supplier, Long> {
    
    fun findByEmail(email: String): Supplier?
    
    fun findByIsActiveTrue(): List<Supplier>
    
    @Query("SELECT s FROM Supplier s WHERE s.name LIKE %:name% AND s.isActive = true")
    fun findActiveSuppliersByNameContaining(@Param("name") name: String): List<Supplier>
    
    fun existsByEmail(email: String): <PERSON>olean
}
