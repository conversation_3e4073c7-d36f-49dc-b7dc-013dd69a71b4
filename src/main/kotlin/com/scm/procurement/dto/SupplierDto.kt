package com.scm.procurement.dto

import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

data class CreateSupplierRequest(
    @field:NotBlank(message = "Supplier name is required")
    @field:Size(max = 100, message = "Supplier name must not exceed 100 characters")
    val name: String,
    
    @field:Email(message = "Email should be valid")
    val email: String?,
    
    @field:Size(max = 20, message = "Phone number must not exceed 20 characters")
    val phone: String?,
    
    @field:Size(max = 200, message = "Address must not exceed 200 characters")
    val address: String?
)

data class UpdateSupplierRequest(
    @field:Size(max = 100, message = "Supplier name must not exceed 100 characters")
    val name: String?,
    
    @field:Email(message = "Email should be valid")
    val email: String?,
    
    @field:Size(max = 20, message = "Phone number must not exceed 20 characters")
    val phone: String?,
    
    @field:Size(max = 200, message = "Address must not exceed 200 characters")
    val address: String?,
    
    val isActive: Boolean?
)

data class SupplierResponse(
    val id: Long,
    val name: String,
    val email: String?,
    val phone: String?,
    val address: String?,
    val isActive: Boolean,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)
