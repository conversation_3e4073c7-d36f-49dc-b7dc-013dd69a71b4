package com.scm.procurement.model

import jakarta.persistence.*
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@Entity
@Table(name = "suppliers")
data class Supplier(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,
    
    @field:NotBlank(message = "Supplier name is required")
    @field:Size(max = 100, message = "Supplier name must not exceed 100 characters")
    @Column(nullable = false, length = 100)
    val name: String,
    
    @field:Email(message = "Email should be valid")
    @Column(unique = true)
    val email: String?,
    
    @field:Size(max = 20, message = "Phone number must not exceed 20 characters")
    val phone: String?,
    
    @field:Size(max = 200, message = "Address must not exceed 200 characters")
    val address: String?,
    
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
