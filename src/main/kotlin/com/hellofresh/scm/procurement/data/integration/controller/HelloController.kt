package com.hellofresh.scm.procurement.data.integration.controller

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/v1")
class HelloController {

    @GetMapping("/hello")
    fun hello(@RequestParam(defaultValue = "World") name: String): ResponseEntity<HelloResponse> {
        val response = HelloResponse(
            message = "Hello, $name!",
            timestamp = LocalDateTime.now(),
            service = "SCM Procurement Data Integration Service"
        )
        return ResponseEntity.ok(response)
    }

    @GetMapping("/health")
    fun health(): ResponseEntity<HealthResponse> {
        val response = HealthResponse(
            status = "UP",
            timestamp = LocalDateTime.now(),
            service = "SCM Procurement Data Integration Service",
            version = "0.0.1-SNAPSHOT"
        )
        return ResponseEntity.ok(response)
    }
}

data class HelloResponse(
    val message: String,
    val timestamp: LocalDateTime,
    val service: String
)

data class HealthResponse(
    val status: String,
    val timestamp: LocalDateTime,
    val service: String,
    val version: String
)
