[versions]
kotlin = "2.0.10"
springBoot = "3.4.5"
springCloud = "2024.0.0"
detekt = "1.23.7"
jackson = "2.19.2"
testcontainers = "1.18.3"

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }

detekt-gradle = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
jacocolog = { id = "org.barfuin.gradle.jacocolog", version = "3.1.0" }
sonarqube = { id = "org.sonarqube", version = "5.1.0.4882" }
spring-boot = { id = "org.springframework.boot", version.ref = "springBoot" }
spring-dependency-management = { id = "io.spring.dependency-management", version = "1.1.7" }

[libraries]
# API
springdoc-openapi = { module = "org.springdoc:springdoc-openapi-starter-webmvc-ui", version = "2.8.9" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }

# Database
postgresql = { module = "org.postgresql:postgresql", version = "42.7.7" }
hibernate-core = { module = "org.hibernate:hibernate-core", version = "6.6.24.Final" }
spring-data-commons = { module = "org.springframework.data:spring-data-commons", version = "3.5.2" }

# Testing
junit-jupiter-params = { module = "org.junit.jupiter:junit-jupiter-params", version = "5.13.4" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version = "4.1.0" }
hamcrest = { module = "org.hamcrest:hamcrest-library", version = "2.2" }
