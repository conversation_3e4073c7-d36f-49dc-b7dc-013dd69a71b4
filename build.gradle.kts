import io.gitlab.arturbosch.detekt.Detekt
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    id("java-base")
    alias(libs.plugins.jacocolog)
    alias(libs.plugins.sonarqube)
    alias(libs.plugins.detekt.gradle)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependency.management)
    id("com.google.cloud.tools.jib") version "3.4.2" apply false
}

group = "com.hellofresh.scm"
version = "0.0.1-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
}

dependencies {
    detektPlugins("io.gitlab.arturbosch.detekt:detekt-formatting:1.23.7")


    // Spring Boot starters
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    
    // Jackson & Kotlin
    implementation(libs.jackson.module.kotlin)
    implementation("com.fasterxml.jackson.core:jackson-annotations")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    
    // Kotlin
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    
    // Database
    implementation(libs.postgresql)
    
    // Logging
    implementation("org.springframework.boot:spring-boot-starter-log4j2")
    implementation("org.apache.logging.log4j:log4j-layout-template-json")
    
    // Metrics & Monitoring
    implementation("io.micrometer:micrometer-registry-prometheus")
    
    // Configuration
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    
    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation(kotlin("test"))
    testImplementation(libs.junit.jupiter.params)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.hamcrest)
}

configurations {
    implementation {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

tasks.test {
    finalizedBy(tasks.jacocoTestReport)
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)
    reports {
        xml.required.set(true)
        html.required.set(true)
    }
}

sonarqube {
    properties {
        property(
            "sonar.exclusions",
            "**/com/hellofresh/scm/**/***Configuration.kt," +
                    "**/com/hellofresh/scm/**/***Application.kt,",
        )
        property(
            "sonar.coverage.exclusions",
            "**/com/hellofresh/scm/model/**/**"
        )
        property("sonar.host.url", "https://sonarqube.tools-k8s.hellofresh.io")
        property("sonar.links.homepage", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.ci", "https://ci.hellofresh.io/teams/scm.procurement/pipelines/scm-procurement-data-integration")
        property("sonar.links.scm", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.scm_dev", "**************:hellofresh/scm-procurement-data-integration.git")
        property("sonar.gradle.skipCompile", "true")
        property(
            "sonar.coverage.jacoco.xmlReportPaths",
            "${project.layout.buildDirectory.get().asFile}/reports/jacoco/jacocoAggregatedReport/jacocoAggregatedReport.xml",
        )
    }
}

tasks {
    compileKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }

    withType<Detekt>().configureEach {
        val javaVersion = "21"

        config.from("$rootDir/.detekt.yaml")

        ignoreFailures = false
        jvmTarget = javaVersion
        buildUponDefaultConfig = true
        parallel = true

        setSource(files(projectDir))
        include("**/*.kt", "**/*.kts")
        exclude("**/resources/**", "**/build/**")

        val ci = System.getenv().containsKey("CI")
        reports {
            txt.required.set(false)
            html.required.set(!ci)
            xml.required.set(ci)
        }
    }

    register<Detekt>("detektFormat") {
        description = "Reformat all Kotlin files."
        autoCorrect = true
        ignoreFailures = true
    }

    check {
        dependsOn("detekt")
    }

    val cleanDetekt by registering(Delete::class) {
        delete(detekt.get().reportsDir.get())
    }

    clean {
        dependsOn(cleanDetekt)
    }
}

// Add integration test task
tasks.register<Test>("integrationTest") {
    description = "Runs integration tests."
    group = "verification"
    
    useJUnitPlatform {
        includeTags("integration")
    }
    
    shouldRunAfter(tasks.test)
}
