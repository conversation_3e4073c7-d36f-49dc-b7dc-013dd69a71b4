name: Master - Continuous Deployment

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}

permissions:
  id-token: write
  contents: read

on:
  push:
    branches:
      - master

jobs:
  sonarqube_analysis:
    name: 🟠 Sonarqube analysis
    runs-on: [ self-hosted, default ]
    steps:
      - name: 🛎️ Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 🗄️ Import secrets
        id: vault-secrets
        uses: ./.github/actions/vault
        with:
          shared-secrets: |
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;
            common/data/defaults SONAR_TOKEN | SONAR_TOKEN;

      - name: 🧪 Gradle Tests, Jacoco and Sonarqube
        shell: bash
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
        run: |
          gradle check test jacocoAggregatedReport sonar \
            "-Dsonar.token=${{ steps.vault-secrets.outputs.SONAR_TOKEN }}"

  prepare_modified_files:
    name: 🗃️ Modules modified
    runs-on: [ self-hosted, default ]
    steps:
      - name: 🛎️ Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 📖 Read config file
        id: config
        run: echo "config=$(jq -c . ./.github/config/modules.json)" >> $GITHUB_OUTPUT

      - name: 👀 Get modified modules
        id: check_modified
        uses: ./.github/actions/check_modified
        with:
          config: ${{ steps.config.outputs.config }}
          terraform_path: 'terraform/**'
          db_migration_path: 'db-migration/**'
          common_paths: ".github/**, model/**, .java-version,gradle/**, build.gradle.kts, gradle.properties, project.properties, settings.gradle.kts"

    outputs:
      modules: ${{ steps.check_modified.outputs.modified_modules }}
      is_terraform_changed: ${{ steps.check_modified.outputs.terraform == 'true' }}
      is_db_migration_changed: ${{ steps.check_modified.outputs.db_migration == 'true' }}

  # ==== TERRAFORM JOB ====
  terraform_apply:
    name: ☸️ Apply terraform changes for Staging and Live
    needs: prepare_modified_files
    runs-on: [ self-hosted, default ]
    if: ${{ always() && !failure() && !cancelled() && needs.prepare_modified_files.outputs.is_terraform_changed == 'true' }}
    timeout-minutes: 120
    steps:
      - name: 🛎️ Checkout source code
        uses: actions/checkout@v3

      - uses: ./.github/actions/terraform_apply
