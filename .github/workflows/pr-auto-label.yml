---
# Automatically adds labels to a PR if it is opened against master
name: Check Labels

on:
    pull_request:
        branches:
            - master
        types:
            - opened

permissions: write-all

jobs:
    auto_label:
        name: Auto Label PR
        runs-on: [self-hosted, default]
        timeout-minutes: 5
        steps:
            - name: 🛎️ Checkout source code
              uses: actions/checkout@v3

            - name: 🏷️ Add SCM Labels
              uses: actions/github-script@v6
              if: ${{ contains(github.head_ref, 'SCM') }}
              with:
                  script: |
                      github.rest.issues.addLabels({
                          issue_number: context.issue.number,
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          labels: ['squad: scm-team', 'tribe: supply-chain-management']
                      })
