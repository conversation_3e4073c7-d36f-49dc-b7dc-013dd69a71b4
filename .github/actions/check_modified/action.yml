name: 'Check Modified Modules'
description: 'Checks which modules have been modified in a PR'
inputs:
  config:
    description: 'Configuration JSON for modules'
    required: true
  terraform_path:
    description: 'Path pattern for terraform files'
    required: false
    default: 'terraform/**'
  db_migration_path:
    description: 'Path pattern for db migration files'
    required: false
    default: 'db-migration/**'
  common_paths:
    description: 'Common paths that affect all modules'
    required: false
    default: '.github/**'
outputs:
  modified_modules:
    description: 'JSON array of modified modules'
  terraform:
    description: 'Whether terraform files were modified'
  db_migration:
    description: 'Whether db migration files were modified'
runs:
  using: "composite"
  steps:
    - name: Get changed files
      id: changed-files
      uses: tj-actions/changed-files@v44
      with:
        files: |
          src/**
          build.gradle.kts
          gradle.properties
          settings.gradle.kts
          .java-version
          
    - name: Check for terraform changes
      id: terraform-check
      uses: tj-actions/changed-files@v44
      with:
        files: ${{ inputs.terraform_path }}
        
    - name: Check for db migration changes
      id: db-migration-check
      uses: tj-actions/changed-files@v44
      with:
        files: ${{ inputs.db_migration_path }}
        
    - name: Determine modified modules
      shell: bash
      run: |
        if [ "${{ steps.changed-files.outputs.any_changed }}" == "true" ]; then
          echo "modified_modules=[{\"module\": \"scm-procurement-data-integration\"}]" >> $GITHUB_OUTPUT
        else
          echo "modified_modules=[]" >> $GITHUB_OUTPUT
        fi
        echo "terraform=${{ steps.terraform-check.outputs.any_changed }}" >> $GITHUB_OUTPUT
        echo "db_migration=${{ steps.db-migration-check.outputs.any_changed }}" >> $GITHUB_OUTPUT
