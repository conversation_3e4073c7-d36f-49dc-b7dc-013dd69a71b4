name: 'Import Vault Secrets'
description: 'Imports secrets from HashiCorp Vault'
inputs:
  shared-secrets:
    description: 'Shared secrets to import'
    required: true
outputs:
  ARTIFACTORY_USERNAME:
    description: 'Artifactory username'
  ARTIFACTORY_PASSWORD:
    description: 'Artifactory password'
  SONAR_TOKEN:
    description: 'SonarQube token'
runs:
  using: "composite"
  steps:
    - name: Set mock secrets for local development
      shell: bash
      run: |
        echo "ARTIFACTORY_USERNAME=mock-username" >> $GITHUB_OUTPUT
        echo "ARTIFACTORY_PASSWORD=mock-password" >> $GITHUB_OUTPUT
        echo "SONAR_TOKEN=mock-sonar-token" >> $GITHUB_OUTPUT
