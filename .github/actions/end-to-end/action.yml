name: 'End-to-End Tests'
description: 'Runs end-to-end tests for the application'
inputs:
  max_attempts:
    description: 'Maximum number of retry attempts'
    required: false
    default: '1'
  retry_on:
    description: 'Retry on error'
    required: false
    default: 'error'
  orderManagementBackendTag:
    description: 'Backend tag for order management'
    required: false
    default: 'latest'
runs:
  using: "composite"
  steps:
    - name: Run end-to-end tests
      shell: bash
      run: |
        echo "Running end-to-end tests..."
        echo "Max attempts: ${{ inputs.max_attempts }}"
        echo "Retry on: ${{ inputs.retry_on }}"
        echo "Backend tag: ${{ inputs.orderManagementBackendTag }}"
        # Add actual e2e test commands here
        ./gradlew integrationTest
