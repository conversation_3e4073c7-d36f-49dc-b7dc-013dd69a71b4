# SCM Procurement Data Integration Service

A Spring Boot application for SCM procurement data integration, built following HelloFresh development standards.

## Features

- Spring Boot 3.4.2 with Kotlin 2.2.0
- RESTful API endpoints
- Health monitoring with Spring Boot Actuator
- Prometheus metrics integration
- Structured JSON logging with Log4j2
- JaCoCo test coverage
- Java 21 support

## Quick Start

### Prerequisites

- Java 21
- Docker (optional)

### Running the Application

```bash
# Build the project
./gradlew build

# Run the application
./gradlew bootRun

# Run tests
./gradlew test
```

### API Endpoints

- `GET /api/v1/hello` - Hello world endpoint
- `GET /api/v1/hello?name=YourName` - Personalized greeting
- `GET /api/v1/health` - Application health status
- `GET /actuator/health` - Spring Boot health endpoint
- `GET /actuator/metrics` - Application metrics
- `GET /actuator/prometheus` - Prometheus metrics

### Example Requests

```bash
# Basic hello
curl http://localhost:8080/api/v1/hello

# Personalized greeting
curl "http://localhost:8080/api/v1/hello?name=HelloFresh"

# Health check
curl http://localhost:8080/api/v1/health

# Actuator health
curl http://localhost:8080/actuator/health
```

## Docker

```bash
# Build Docker image
make docker-build

# Run with Docker
make docker-run
```

## Development

### Project Structure

```
src/
├── main/
│   ├── kotlin/
│   │   └── com/hellofresh/scm/procurement/data/integration/
│   │       ├── ProcurementDataIntegrationApplication.kt
│   │       └── controller/
│   │           └── HelloController.kt
│   └── resources/
│       └── application.yml
└── test/
    ├── kotlin/
    │   └── com/hellofresh/scm/procurement/data/integration/
    │       ├── ProcurementDataIntegrationApplicationTest.kt
    │       └── controller/
    │           └── HelloControllerTest.kt
    └── resources/
        └── application-test.yml
```

### Available Make Commands

- `make build` - Build the project
- `make test` - Run tests
- `make run` - Run the application
- `make clean` - Clean build artifacts
- `make docker-build` - Build Docker image
- `make docker-run` - Run with Docker

## Technology Stack

- **Framework**: Spring Boot 3.4.5
- **Language**: Kotlin 2.2.0
- **Build Tool**: Gradle 9.0.0
- **Java Version**: 21
- **Testing**: JUnit 5, MockMvc
- **Logging**: Log4j2 with JSON layout
- **Metrics**: Micrometer with Prometheus
- **Containerization**: Docker with Distroless base image

## 🔄 CI/CD Pipeline

The project includes comprehensive GitHub Actions workflows:

### 🚀 Workflows

- **CI Pipeline** (`.github/workflows/ci.yml`) - Runs on PRs
  - Build, test, and code quality analysis
  - Integration tests
  - Test coverage reporting with Codecov
  - Docker image building
  - Code quality checks with Detekt

- **Release Pipeline** (`.github/workflows/release.yml`) - Runs on master/main
  - Automated versioning
  - Docker image build and push to GHCR
  - GitHub release creation with changelog
  - Multi-platform builds (amd64/arm64)

- **Security Scanning** (`.github/workflows/security.yml`)
  - CodeQL analysis for security vulnerabilities
  - Trivy security scanning
  - Dependency vulnerability checks

- **Dependency Review** (`.github/workflows/dependency-review.yml`)
  - Automated dependency vulnerability scanning on PRs
  - License compliance checking

### 🤖 Automated Features

- **Dependabot** - Weekly dependency updates
- **Code Owners** - Automatic PR reviewers assignment
- **PR Templates** - Standardized pull request format
- **Security Scanning** - Automated vulnerability detection

## HelloFresh Standards

This project follows HelloFresh development guidelines:

- Structured logging with JSON format
- Health checks and monitoring
- Prometheus metrics integration
- Test coverage with JaCoCo
- Docker containerization
- Makefile for common operations
- Gradle wrapper for reproducible builds
- GitHub Actions CI/CD pipelines
- Code quality enforcement with Detekt
- Security scanning and dependency management
